#!/usr/bin/env python3
"""
Test script to verify Google Calendar integration is working properly.
This script will help diagnose and fix the Google Calendar integration warning.
"""

import os
import sys
import logging

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_google_calendar_imports():
    """Test if Google Calendar API libraries can be imported."""
    print("=" * 60)
    print("TESTING GOOGLE CALENDAR API IMPORTS")
    print("=" * 60)
    
    try:
        from google.oauth2.credentials import Credentials
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        from googleapiclient.errors import HttpError
        print("✅ All Google Calendar API libraries imported successfully!")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Google Calendar API libraries: {e}")
        print("\n🔧 SOLUTION: Install missing dependencies:")
        print("   pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client")
        return False

def test_credentials_file():
    """Test if the Google Calendar credentials file exists and is valid."""
    print("\n" + "=" * 60)
    print("TESTING GOOGLE CALENDAR CREDENTIALS FILE")
    print("=" * 60)
    
    # Check multiple possible paths
    possible_paths = [
        "credentials/google-calendar-credentials.json",
        os.path.join(os.path.dirname(__file__), "credentials", "google-calendar-credentials.json"),
        os.path.join(os.path.dirname(__file__), "..", "credentials", "google-calendar-credentials.json")
    ]
    
    for path in possible_paths:
        abs_path = os.path.abspath(path)
        print(f"Checking: {abs_path}")
        
        if os.path.exists(abs_path):
            print(f"✅ Credentials file found at: {abs_path}")
            
            # Try to load and validate the credentials
            try:
                import json
                with open(abs_path, 'r') as f:
                    creds_data = json.load(f)
                
                required_fields = ['type', 'project_id', 'private_key', 'client_email']
                missing_fields = [field for field in required_fields if field not in creds_data]
                
                if missing_fields:
                    print(f"❌ Credentials file is missing required fields: {missing_fields}")
                    return False, abs_path
                else:
                    print("✅ Credentials file appears to be valid!")
                    return True, abs_path
                    
            except json.JSONDecodeError as e:
                print(f"❌ Credentials file is not valid JSON: {e}")
                return False, abs_path
            except Exception as e:
                print(f"❌ Error reading credentials file: {e}")
                return False, abs_path
    
    print("❌ No credentials file found in any of the expected locations")
    print("\n🔧 SOLUTION: Ensure your Google Calendar service account credentials are saved as:")
    print("   credentials/google-calendar-credentials.json")
    return False, None

def test_calendar_service_initialization():
    """Test if the Google Calendar service can be initialized."""
    print("\n" + "=" * 60)
    print("TESTING GOOGLE CALENDAR SERVICE INITIALIZATION")
    print("=" * 60)
    
    # First check if imports work
    if not test_google_calendar_imports():
        return False
    
    # Check credentials
    creds_valid, creds_path = test_credentials_file()
    if not creds_valid:
        return False
    
    try:
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        
        credentials = service_account.Credentials.from_service_account_file(
            creds_path,
            scopes=['https://www.googleapis.com/auth/calendar.readonly']
        )
        calendar_service = build('calendar', 'v3', credentials=credentials)
        print("✅ Google Calendar service initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize Google Calendar service: {e}")
        print("\n🔧 POSSIBLE SOLUTIONS:")
        print("   1. Check that your service account has Calendar API enabled")
        print("   2. Verify the service account has access to the calendars you want to check")
        print("   3. Ensure the credentials file contains the correct private key")
        return False

def test_assignment_agent_integration():
    """Test the assignment agent's Google Calendar integration."""
    print("\n" + "=" * 60)
    print("TESTING ASSIGNMENT AGENT GOOGLE CALENDAR INTEGRATION")
    print("=" * 60)
    
    try:
        # Import the assignment agent
        from agents.assignment_agent import AssignmentAgentIntegration
        
        # Create a mock database connection (None for testing)
        creds_path = os.path.join(os.path.dirname(__file__), "credentials", "google-calendar-credentials.json")
        
        # Initialize the assignment agent with calendar credentials
        agent = AssignmentAgentIntegration(
            db_connection=None,  # Mock for testing
            google_calendar_credentials_path=creds_path
        )
        
        if agent.calendar_service is not None:
            print("✅ Assignment agent Google Calendar integration is working!")
            return True
        else:
            print("❌ Assignment agent calendar service is None")
            return False
            
    except Exception as e:
        print(f"❌ Error testing assignment agent integration: {e}")
        return False

def main():
    """Run all Google Calendar integration tests."""
    print("🔍 GOOGLE CALENDAR INTEGRATION DIAGNOSTIC TOOL")
    print("This tool will help identify and fix Google Calendar integration issues.\n")
    
    tests = [
        ("Google Calendar API Imports", test_google_calendar_imports),
        ("Credentials File", lambda: test_credentials_file()[0]),
        ("Calendar Service Initialization", test_calendar_service_initialization),
        ("Assignment Agent Integration", test_assignment_agent_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Google Calendar integration should be working.")
        print("The warning should no longer appear in your assignment agent.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please address the issues above.")
        print("Once fixed, the Google Calendar integration warning should disappear.")

if __name__ == "__main__":
    main()
