[{"Field": "creatortype", "Value": 1, "Label": "Resource"}, {"Field": "creatortype", "Value": 2, "Label": "Contact"}, {"Field": "issuetype", "Value": 4, "Label": "Hardware"}, {"Field": "issuetype", "Value": 5, "Label": "Software/SaaS"}, {"Field": "issuetype", "Value": 6, "Label": "Network"}, {"Field": "issuetype", "Value": 7, "Label": "Assessment"}, {"Field": "issuetype", "Value": 8, "Label": "Server"}, {"Field": "issuetype", "Value": 9, "Label": "Active Directory"}, {"Field": "issuetype", "Value": 10, "Label": "Telephony"}, {"Field": "issuetype", "Value": 11, "Label": "Cloud Workspace"}, {"Field": "issuetype", "Value": 12, "Label": "Apple"}, {"Field": "issuetype", "Value": 13, "Label": "Backup"}, {"Field": "issuetype", "Value": 14, "Label": "Cybersecurity Intrusion"}, {"Field": "issuetype", "Value": 15, "Label": "Email"}, {"Field": "issuetype", "Value": 16, "Label": "OS"}, {"Field": "issuetype", "Value": 17, "Label": "Other"}, {"Field": "issuetype", "Value": 18, "Label": "Printer"}, {"Field": "issuetype", "Value": 19, "Label": "Sales"}, {"Field": "issuetype", "Value": 20, "Label": "Windows"}, {"Field": "issuetype", "Value": 21, "Label": "Administration"}, {"Field": "issuetype", "Value": 22, "Label": "Triage"}, {"Field": "issuetype", "Value": 23, "Label": "User Admin"}, {"Field": "issuetype", "Value": 25, "Label": "Threat Protection"}, {"Field": "issuetype", "Value": 26, "Label": "Business Continuity"}, {"Field": "lastactivitypersontype", "Value": 1, "Label": "Resource"}, {"Field": "lastactivitypersontype", "Value": 2, "Label": "Contact"}, {"Field": "priority", "Value": 1, "Label": "High"}, {"Field": "priority", "Value": 2, "Label": "Medium"}, {"Field": "priority", "Value": 3, "Label": "Low"}, {"Field": "priority", "Value": 4, "Label": "Critical"}, {"Field": "priority", "Value": 5, "Label": "Desktop/User Down"}, {"Field": "queueid", "Value": 5, "Label": "Client Portal"}, {"Field": "queueid", "Value": 6, "Label": "Post Sale"}, {"Field": "queueid", "Value": 8, "Label": "Do Not Use"}, {"Field": "queueid", "Value": 14046773, "Label": "Tier 1"}, {"Field": "queueid", "Value": 29682824, "Label": "Monitoring"}, {"Field": "queueid", "Value": 29682858, "Label": "Triage"}, {"Field": "queueid", "Value": 29682859, "Label": "Tier III"}, {"Field": "queueid", "Value": 29682863, "Label": "GMS Helpdesk"}, {"Field": "queueid", "Value": 29682864, "Label": "Escalation Team"}, {"Field": "queueid", "Value": 29682865, "Label": "Support Desk Team"}, {"Field": "queueid", "Value": 29682867, "Label": "Sales"}, {"Field": "queueid", "Value": 29682869, "Label": "Administration"}, {"Field": "queueid", "Value": 29682875, "Label": "Spam"}, {"Field": "queueid", "Value": 29682877, "Label": "Business Operations"}, {"Field": "queueid", "Value": 29682879, "Label": "HDSS"}, {"Field": "queueid", "Value": 29682881, "Label": "<PERSON>rged"}, {"Field": "queueid", "Value": 29682882, "Label": "Mission Control"}, {"Field": "servicelevelagreementid", "Value": 1, "Label": "Standard"}, {"Field": "source", "Value": -2, "Label": "Insourced"}, {"Field": "source", "Value": -1, "Label": "Client Portal"}, {"Field": "source", "Value": 1, "Label": "Other"}, {"Field": "source", "Value": 2, "Label": "Call"}, {"Field": "source", "Value": 3, "Label": "Voice Mail"}, {"Field": "source", "Value": 4, "Label": "Email"}, {"Field": "source", "Value": 6, "Label": "Verbal"}, {"Field": "source", "Value": 13, "Label": "Central Management"}, {"Field": "source", "Value": 14, "Label": "Sophos Central"}, {"Field": "status", "Value": 1, "Label": "New"}, {"Field": "status", "Value": 5, "Label": "Complete"}, {"Field": "status", "Value": 7, "Label": "Waiting Customer"}, {"Field": "status", "Value": 8, "Label": "Response Received"}, {"Field": "status", "Value": 10, "Label": "Scheduled"}, {"Field": "status", "Value": 12, "Label": "Help Desk"}, {"Field": "status", "Value": 13, "Label": "Follow Up"}, {"Field": "status", "Value": 14, "Label": "Waiting Parts"}, {"Field": "status", "Value": 15, "Label": "In Progress"}, {"Field": "status", "Value": 16, "Label": "Waiting Vendor"}, {"Field": "status", "Value": 17, "Label": "Waiting Customer 2"}, {"Field": "status", "Value": 19, "Label": "Waiting Customer 1"}, {"Field": "status", "Value": 21, "Label": "End User Contacted"}, {"Field": "status", "Value": 22, "Label": "Pending End User Confirm"}, {"Field": "status", "Value": 23, "Label": "Update for Helpdesk"}, {"Field": "status", "Value": 24, "Label": "Escalated Level3 Required"}, {"Field": "status", "Value": 25, "Label": "Dispatch – Docs Required"}, {"Field": "status", "Value": 26, "Label": "Dispatch – Tools Unavail"}, {"Field": "status", "Value": 27, "Label": "Beyond Helpdesk Scope"}, {"Field": "status", "Value": 28, "Label": "Scheduled: Onsite"}, {"Field": "status", "Value": 29, "Label": "Client Non-Responsive"}, {"Field": "status", "Value": 30, "Label": "Helpdesk Steps Complete"}, {"Field": "status", "Value": 31, "Label": "Level 3 Take Back"}, {"Field": "status", "Value": 32, "Label": "Dispatch – Level3 Process"}, {"Field": "status", "Value": 33, "Label": "Assigned"}, {"Field": "status", "Value": 35, "Label": "Email End User Custom"}, {"Field": "status", "Value": 36, "Label": "Auto-Close No Feedback"}, {"Field": "status", "Value": 38, "Label": "Follow-Up Required"}, {"Field": "status", "Value": 39, "Label": "Scheduled: Remote"}, {"Field": "status", "Value": 40, "Label": "Escalated - CS Lead"}, {"Field": "status", "Value": 41, "Label": "Escalated - Engineering"}, {"Field": "status", "Value": 42, "Label": "Escalated - Next Level"}, {"Field": "status", "Value": 43, "Label": "Escalated - GMS"}, {"Field": "status", "Value": 44, "Label": "Escalated - Sales"}, {"Field": "status", "Value": 45, "Label": "Escalated - CS Manager"}, {"Field": "status", "Value": 46, "Label": "Task Pending"}, {"Field": "status", "Value": 47, "Label": "Task Complete"}, {"Field": "status", "Value": 49, "Label": "Waiting Approval"}, {"Field": "status", "Value": 50, "Label": "Escalated - Triage I"}, {"Field": "status", "Value": 51, "Label": "Escalated - Triage II"}, {"Field": "status", "Value": 52, "Label": "Resolved"}, {"Field": "status", "Value": 53, "Label": "Proof of Concept"}, {"Field": "status", "Value": 54, "Label": "Escalation Required"}, {"Field": "status", "Value": 55, "Label": "Escalate from MC"}, {"Field": "status", "Value": 56, "Label": "MC - Needs Info"}, {"Field": "status", "Value": 57, "Label": "MC - Out of Scope"}, {"Field": "status", "Value": 58, "Label": "Escalate to MC"}, {"Field": "subissuetype", "Value": 11, "Label": "Equipment Move"}, {"Field": "subissuetype", "Value": 12, "Label": "Keyboard/Mouse"}, {"Field": "subissuetype", "Value": 13, "Label": "Laptop"}, {"Field": "subissuetype", "Value": 14, "Label": "Monitor(s)"}, {"Field": "subissuetype", "Value": 19, "Label": "<PERSON><PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 20, "Label": "VPN"}, {"Field": "subissuetype", "Value": 21, "Label": "Wireless"}, {"Field": "subissuetype", "Value": 23, "Label": "Migrate"}, {"Field": "subissuetype", "Value": 24, "Label": "Provision"}, {"Field": "subissuetype", "Value": 25, "Label": "Other"}, {"Field": "subissuetype", "Value": 26, "Label": "z"}, {"Field": "subissuetype", "Value": 27, "Label": "Security"}, {"Field": "subissuetype", "Value": 28, "Label": "Comprehensive"}, {"Field": "subissuetype", "Value": 29, "Label": "Network"}, {"Field": "subissuetype", "Value": 30, "Label": "Inventory"}, {"Field": "subissuetype", "Value": 31, "Label": "Other"}, {"Field": "subissuetype", "Value": 32, "Label": "Performance"}, {"Field": "subissuetype", "Value": 33, "Label": "Workstation/Server"}, {"Field": "subissuetype", "Value": 34, "Label": "Data Backup"}, {"Field": "subissuetype", "Value": 35, "Label": "Cloud Storage (SharePoint, OneDrive, etc.)"}, {"Field": "subissuetype", "Value": 36, "Label": "Business Line Application"}, {"Field": "subissuetype", "Value": 37, "Label": "Chrome"}, {"Field": "subissuetype", "Value": 38, "Label": "Edge/IE"}, {"Field": "subissuetype", "Value": 39, "Label": "Adobe"}, {"Field": "subissuetype", "Value": 40, "Label": "AV/Anti-Spyware"}, {"Field": "subissuetype", "Value": 41, "Label": "Browser"}, {"Field": "subissuetype", "Value": 42, "Label": "z"}, {"Field": "subissuetype", "Value": 43, "Label": "Other"}, {"Field": "subissuetype", "Value": 44, "Label": "z"}, {"Field": "subissuetype", "Value": 45, "Label": "Printer"}, {"Field": "subissuetype", "Value": 46, "Label": "Wipe and Restore"}, {"Field": "subissuetype", "Value": 47, "Label": "Other"}, {"Field": "subissuetype", "Value": 48, "Label": "Workstation"}, {"Field": "subissuetype", "Value": 49, "Label": "AD Profile Change"}, {"Field": "subissuetype", "Value": 50, "Label": "New Hire"}, {"Field": "subissuetype", "Value": 51, "Label": "File Permissions"}, {"Field": "subissuetype", "Value": 52, "Label": "Offboarding Request"}, {"Field": "subissuetype", "Value": 53, "Label": "Other"}, {"Field": "subissuetype", "Value": 54, "Label": "Mobile"}, {"Field": "subissuetype", "Value": 55, "Label": "Phone Issue"}, {"Field": "subissuetype", "Value": 56, "Label": "z"}, {"Field": "subissuetype", "Value": 57, "Label": "Voicemail (Setup/Password Reset)"}, {"Field": "subissuetype", "Value": 58, "Label": "Other"}, {"Field": "subissuetype", "Value": 59, "Label": "New User Account"}, {"Field": "subissuetype", "Value": 60, "Label": "Remove User Account"}, {"Field": "subissuetype", "Value": 61, "Label": "Locked Account"}, {"Field": "subissuetype", "Value": 62, "Label": "Service Applications"}, {"Field": "subissuetype", "Value": 63, "Label": "Other"}, {"Field": "subissuetype", "Value": 64, "Label": "Connectivity/Outages"}, {"Field": "subissuetype", "Value": 65, "Label": "Unable to Login/ Password Reset"}, {"Field": "subissuetype", "Value": 66, "Label": "Account Locked Out"}, {"Field": "subissuetype", "Value": 67, "Label": "Password Reset"}, {"Field": "subissuetype", "Value": 68, "Label": "Email"}, {"Field": "subissuetype", "Value": 69, "Label": "Hardware"}, {"Field": "subissuetype", "Value": 70, "Label": "iPhone"}, {"Field": "subissuetype", "Value": 71, "Label": "iPod/iPad"}, {"Field": "subissuetype", "Value": 72, "Label": "Mapped Drives"}, {"Field": "subissuetype", "Value": 73, "Label": "MS Office"}, {"Field": "subissuetype", "Value": 74, "Label": "Network/RDP"}, {"Field": "subissuetype", "Value": 75, "Label": "Other"}, {"Field": "subissuetype", "Value": 76, "Label": "Password/Keychain"}, {"Field": "subissuetype", "Value": 77, "Label": "Performance"}, {"Field": "subissuetype", "Value": 78, "Label": "Print/Scan"}, {"Field": "subissuetype", "Value": 79, "Label": "Backup Failed"}, {"Field": "subissuetype", "Value": 80, "Label": "Change/Configure"}, {"Field": "subissuetype", "Value": 81, "Label": "Check-in/Offline"}, {"Field": "subissuetype", "Value": 82, "Label": "Hardware Issue"}, {"Field": "subissuetype", "Value": 83, "Label": "New Setup"}, {"Field": "subissuetype", "Value": 84, "Label": "Other"}, {"Field": "subissuetype", "Value": 85, "Label": "Rest<PERSON>"}, {"Field": "subissuetype", "Value": 86, "Label": "Screenshot"}, {"Field": "subissuetype", "Value": 87, "Label": "Space Limitations"}, {"Field": "subissuetype", "Value": 88, "Label": "SystemWatch"}, {"Field": "subissuetype", "Value": 89, "Label": "Compromised Credentials"}, {"Field": "subissuetype", "Value": 90, "Label": "Denial-of-Service Attack"}, {"Field": "subissuetype", "Value": 91, "Label": "Malware Attack"}, {"Field": "subissuetype", "Value": 92, "Label": "Phishing Attack"}, {"Field": "subissuetype", "Value": 93, "Label": "Virus"}, {"Field": "subissuetype", "Value": 94, "Label": "Other"}, {"Field": "subissuetype", "Value": 95, "Label": "Firefox"}, {"Field": "subissuetype", "Value": 96, "Label": "Email"}, {"Field": "subissuetype", "Value": 97, "Label": "MS Office Apps (Excel, Word, etc.)"}, {"Field": "subissuetype", "Value": 98, "Label": "QuickBooks"}, {"Field": "subissuetype", "Value": 99, "Label": "Other"}, {"Field": "subissuetype", "Value": 100, "Label": "<PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 101, "Label": "Email Signature"}, {"Field": "subissuetype", "Value": 102, "Label": "Configure/Fix"}, {"Field": "subissuetype", "Value": 103, "Label": "Contacts"}, {"Field": "subissuetype", "Value": 104, "Label": "Mailbox Permissions"}, {"Field": "subissuetype", "Value": 105, "Label": "Mobile Device"}, {"Field": "subissuetype", "Value": 106, "Label": "Outlook Setup"}, {"Field": "subissuetype", "Value": 107, "Label": "Other"}, {"Field": "subissuetype", "Value": 108, "Label": "Calendar"}, {"Field": "subissuetype", "Value": 109, "Label": "Spam"}, {"Field": "subissuetype", "Value": 110, "Label": "Password Reset"}, {"Field": "subissuetype", "Value": 111, "Label": "Disk Space"}, {"Field": "subissuetype", "Value": 112, "Label": "Firewall"}, {"Field": "subissuetype", "Value": 113, "Label": "Switch"}, {"Field": "subissuetype", "Value": 114, "Label": "New Workstation Setup"}, {"Field": "subissuetype", "Value": 115, "Label": "Server"}, {"Field": "subissuetype", "Value": 116, "Label": "Access Point"}, {"Field": "subissuetype", "Value": 117, "Label": "Linux"}, {"Field": "subissuetype", "Value": 118, "Label": "MacOS"}, {"Field": "subissuetype", "Value": 119, "Label": "Mac OS X"}, {"Field": "subissuetype", "Value": 120, "Label": "Windows 7"}, {"Field": "subissuetype", "Value": 121, "Label": "Windows"}, {"Field": "subissuetype", "Value": 124, "Label": "Windows 2008"}, {"Field": "subissuetype", "Value": 126, "Label": "Other"}, {"Field": "subissuetype", "Value": 127, "Label": "Firewall"}, {"Field": "subissuetype", "Value": 128, "Label": "WAN"}, {"Field": "subissuetype", "Value": 129, "Label": "Mapped Drives"}, {"Field": "subissuetype", "Value": 130, "Label": "Outage"}, {"Field": "subissuetype", "Value": 131, "Label": "RDP"}, {"Field": "subissuetype", "Value": 132, "Label": "Switch"}, {"Field": "subissuetype", "Value": 133, "Label": "Other"}, {"Field": "subissuetype", "Value": 134, "Label": "Change/Configure"}, {"Field": "subissuetype", "Value": 135, "Label": "New Setup"}, {"Field": "subissuetype", "Value": 136, "Label": "Printing Issue"}, {"Field": "subissuetype", "Value": 137, "Label": "Scanning Issue"}, {"Field": "subissuetype", "Value": 138, "Label": "Hardware"}, {"Field": "subissuetype", "Value": 139, "Label": "Infrastructure"}, {"Field": "subissuetype", "Value": 140, "Label": "Licenses"}, {"Field": "subissuetype", "Value": 141, "Label": "Other"}, {"Field": "subissuetype", "Value": 142, "Label": "Services"}, {"Field": "subissuetype", "Value": 143, "Label": "Fax"}, {"Field": "subissuetype", "Value": 144, "Label": "Mapped Drives"}, {"Field": "subissuetype", "Value": 145, "Label": "MS Office"}, {"Field": "subissuetype", "Value": 146, "Label": "Network/RDP"}, {"Field": "subissuetype", "Value": 147, "Label": "Other"}, {"Field": "subissuetype", "Value": 148, "Label": "Password/Keychain"}, {"Field": "subissuetype", "Value": 149, "Label": "Performance"}, {"Field": "subissuetype", "Value": 150, "Label": "Print/Scan"}, {"Field": "subissuetype", "Value": 151, "Label": "Monitoring Report"}, {"Field": "subissuetype", "Value": 152, "Label": "Allowed List Request"}, {"Field": "subissuetype", "Value": 153, "Label": "Printing/ThinPrint"}, {"Field": "subissuetype", "Value": 154, "Label": "SSL Certifications"}, {"Field": "subissuetype", "Value": 155, "Label": "Conferencing (Zoom, Teams, etc.)"}, {"Field": "subissuetype", "Value": 156, "Label": "Setup/ Config"}, {"Field": "subissuetype", "Value": 157, "Label": "General Break/Fix"}, {"Field": "subissuetype", "Value": 158, "Label": "New Application Download Request"}, {"Field": "subissuetype", "Value": 159, "Label": "Storage/ Performance Alerts"}, {"Field": "subissuetype", "Value": 160, "Label": "<PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 161, "Label": "Distribution Group Add/ Change"}, {"Field": "subissuetype", "Value": 162, "Label": "Report"}, {"Field": "subissuetype", "Value": 163, "Label": "Process Change"}, {"Field": "subissuetype", "Value": 164, "Label": "Other"}, {"Field": "subissuetype", "Value": 165, "Label": "Internal Request"}, {"Field": "subissuetype", "Value": 166, "Label": "Camera/Webcam"}, {"Field": "subissuetype", "Value": 167, "Label": "Docking System"}, {"Field": "subissuetype", "Value": 168, "Label": "Speakers/Headset"}, {"Field": "subissuetype", "Value": 169, "Label": "Other"}, {"Field": "subissuetype", "Value": 170, "Label": "IT Glue"}, {"Field": "subissuetype", "Value": 171, "Label": "HIPAA"}, {"Field": "subissuetype", "Value": 172, "Label": "Compliance"}, {"Field": "subissuetype", "Value": 173, "Label": "CRM"}, {"Field": "subissuetype", "Value": 174, "Label": "Google Workspace"}, {"Field": "subissuetype", "Value": 175, "Label": "Ink"}, {"Field": "subissuetype", "Value": 176, "Label": "Driver"}, {"Field": "subissuetype", "Value": 177, "Label": "Other"}, {"Field": "subissuetype", "Value": 178, "Label": "Auto Attendant/Call Routing"}, {"Field": "subissuetype", "Value": 179, "Label": "Hardware Request"}, {"Field": "subissuetype", "Value": 180, "Label": "New Extension"}, {"Field": "subissuetype", "Value": 181, "Label": "VOIP Connection/Latency"}, {"Field": "subissuetype", "Value": 182, "Label": "Other"}, {"Field": "subissuetype", "Value": 183, "Label": "Billing"}, {"Field": "subissuetype", "Value": 184, "Label": "Technology Business Review"}, {"Field": "subissuetype", "Value": 185, "Label": "New Hire (VDI)"}, {"Field": "subissuetype", "Value": 186, "Label": "File Recovery"}, {"Field": "subissuetype", "Value": 187, "Label": "Email Recovery"}, {"Field": "subissuetype", "Value": 188, "Label": "Contact Update"}, {"Field": "subissuetype", "Value": 189, "Label": "Licensing"}, {"Field": "subissuetype", "Value": 190, "Label": "Multifactor Authentication"}, {"Field": "subissuetype", "Value": 191, "Label": "Offboarding"}, {"Field": "subissuetype", "Value": 192, "Label": "Onboarding"}, {"Field": "subissuetype", "Value": 193, "Label": "Password Reset"}, {"Field": "subissuetype", "Value": 194, "Label": "Permissions"}, {"Field": "subissuetype", "Value": 195, "Label": "Other"}, {"Field": "subissuetype", "Value": 196, "Label": "Bridge"}, {"Field": "subissuetype", "Value": 197, "Label": "DHCP"}, {"Field": "subissuetype", "Value": 198, "Label": "DNS"}, {"Field": "subissuetype", "Value": 199, "Label": "LAN"}, {"Field": "subissuetype", "Value": 200, "Label": "Modem"}, {"Field": "subissuetype", "Value": 201, "Label": "Windows Server"}, {"Field": "subissuetype", "Value": 202, "Label": "Android"}, {"Field": "subissuetype", "Value": 203, "Label": "SSL Certificate"}, {"Field": "subissuetype", "Value": 204, "Label": "DNS"}, {"Field": "subissuetype", "Value": 205, "Label": "Driver"}, {"Field": "subissuetype", "Value": 206, "Label": "FSLogix"}, {"Field": "subissuetype", "Value": 207, "Label": "Group Policy"}, {"Field": "subissuetype", "Value": 208, "Label": "iOS/iPadOS"}, {"Field": "subissuetype", "Value": 209, "Label": "Mapped Drive"}, {"Field": "subissuetype", "Value": 210, "Label": "Print Management"}, {"Field": "subissuetype", "Value": 211, "Label": "Remote Desktop"}, {"Field": "subissuetype", "Value": 212, "Label": "File Share"}, {"Field": "subissuetype", "Value": 213, "Label": "Box"}, {"Field": "subissuetype", "Value": 214, "Label": "Citrix"}, {"Field": "subissuetype", "Value": 215, "Label": "Dell Command Update"}, {"Field": "subissuetype", "Value": 216, "Label": "Dropbox"}, {"Field": "subissuetype", "Value": 217, "Label": "FortiVPN"}, {"Field": "subissuetype", "Value": 218, "Label": "Google Chrome"}, {"Field": "subissuetype", "Value": 219, "Label": "Kaseya VSA"}, {"Field": "subissuetype", "Value": 220, "Label": "LastPass"}, {"Field": "subissuetype", "Value": 221, "Label": "Loom"}, {"Field": "subissuetype", "Value": 222, "Label": "MaaS360"}, {"Field": "subissuetype", "Value": 223, "Label": "Microsoft Dynamics"}, {"Field": "subissuetype", "Value": 224, "Label": "Microsoft Edge"}, {"Field": "subissuetype", "Value": 225, "Label": "Microsoft Excel"}, {"Field": "subissuetype", "Value": 226, "Label": "Microsoft Internet Explorer"}, {"Field": "subissuetype", "Value": 227, "Label": "Microsoft Intune"}, {"Field": "subissuetype", "Value": 228, "Label": "Microsoft OneDrive"}, {"Field": "subissuetype", "Value": 229, "Label": "Microsoft Outlook"}, {"Field": "subissuetype", "Value": 230, "Label": "Microsoft PowerPoint"}, {"Field": "subissuetype", "Value": 231, "Label": "Microsoft SharePoint"}, {"Field": "subissuetype", "Value": 232, "Label": "Microsoft Teams"}, {"Field": "subissuetype", "Value": 233, "Label": "Microsoft Word"}, {"Field": "subissuetype", "Value": 234, "Label": "Native Android App"}, {"Field": "subissuetype", "Value": 235, "Label": "Native Windows App"}, {"Field": "subissuetype", "Value": 236, "Label": "Native iOS/iPadOS App"}, {"Field": "subissuetype", "Value": 237, "Label": "Native Linux App"}, {"Field": "subissuetype", "Value": 238, "Label": "Native MacOS App"}, {"Field": "subissuetype", "Value": 239, "Label": "NetApp"}, {"Field": "subissuetype", "Value": 240, "Label": "Nord<PERSON><PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 241, "Label": "Opera"}, {"Field": "subissuetype", "Value": 242, "Label": "Quo<PERSON><PERSON><PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 243, "Label": "RingCentral"}, {"Field": "subissuetype", "Value": 244, "Label": "Sage"}, {"Field": "subissuetype", "Value": 245, "Label": "<PERSON><PERSON>ck"}, {"Field": "subissuetype", "Value": 246, "Label": "Sophos VPN Client"}, {"Field": "subissuetype", "Value": 247, "Label": "Switchvox"}, {"Field": "subissuetype", "Value": 248, "Label": "<PERSON><PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 249, "Label": "VLC"}, {"Field": "subissuetype", "Value": 250, "Label": "WebEx"}, {"Field": "subissuetype", "Value": 251, "Label": "Zoom"}, {"Field": "subissuetype", "Value": 252, "Label": "8x8"}, {"Field": "subissuetype", "Value": 253, "Label": "ActivTrack"}, {"Field": "subissuetype", "Value": 254, "Label": "Entra Connect"}, {"Field": "subissuetype", "Value": 255, "Label": "Adobe Acrobat"}, {"Field": "subissuetype", "Value": 256, "Label": "Adobe Reader"}, {"Field": "subissuetype", "Value": 257, "Label": "1Password"}, {"Field": "subissuetype", "Value": 258, "Label": "Applied Epic"}, {"Field": "subissuetype", "Value": 259, "Label": "Azure Virtual Desktop"}, {"Field": "subissuetype", "Value": 260, "Label": "Azure Virtual Desktop Client"}, {"Field": "subissuetype", "Value": 261, "Label": "Bluebeam Revu"}, {"Field": "subissuetype", "Value": 262, "Label": "Microsoft Exchange"}, {"Field": "subissuetype", "Value": 263, "Label": "AppRiver Email Threat Protection"}, {"Field": "subissuetype", "Value": 264, "Label": "Avast!"}, {"Field": "subissuetype", "Value": 265, "Label": "BitDefender"}, {"Field": "subissuetype", "Value": 266, "Label": "BlackPoint"}, {"Field": "subissuetype", "Value": 267, "Label": "Crowdstrike"}, {"Field": "subissuetype", "Value": 268, "Label": "Cylance"}, {"Field": "subissuetype", "Value": 269, "Label": "FortiClient"}, {"Field": "subissuetype", "Value": 270, "Label": "FortiMail"}, {"Field": "subissuetype", "Value": 271, "Label": "<PERSON>ress"}, {"Field": "subissuetype", "Value": 272, "Label": "Malwarebytes"}, {"Field": "subissuetype", "Value": 273, "Label": "McAfee"}, {"Field": "subissuetype", "Value": 274, "Label": "Microsoft Defender"}, {"Field": "subissuetype", "Value": 275, "Label": "Microsoft Defender for Azure"}, {"Field": "subissuetype", "Value": 276, "Label": "Norton Antivirus"}, {"Field": "subissuetype", "Value": 277, "Label": "Sophos Email Security"}, {"Field": "subissuetype", "Value": 278, "Label": "Sophos Endpoint Protection"}, {"Field": "subissuetype", "Value": 279, "Label": "Sophos Phishing Simulation"}, {"Field": "subissuetype", "Value": 280, "Label": "Webroot"}, {"Field": "subissuetype", "Value": 281, "Label": "Other"}, {"Field": "subissuetype", "Value": 282, "Label": "Configuration"}, {"Field": "subissuetype", "Value": 283, "Label": "Restore - File"}, {"Field": "subissuetype", "Value": 284, "Label": "Restore - Server"}, {"Field": "subissuetype", "Value": 285, "Label": "Restore - Share"}, {"Field": "subissuetype", "Value": 286, "Label": "Restore - Test"}, {"Field": "subissuetype", "Value": 287, "Label": "SaaS - Acronis"}, {"Field": "subissuetype", "Value": 288, "Label": "SaaS - AppRiver"}, {"Field": "subissuetype", "Value": 289, "Label": "SaaS - <PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 290, "Label": "SaaS - <PERSON><PERSON><PERSON>"}, {"Field": "subissuetype", "Value": 291, "Label": "Troubleshooting"}, {"Field": "subissuetype", "Value": 292, "Label": "Other"}, {"Field": "subissuetype", "Value": 293, "Label": "VoIP Handset"}, {"Field": "subissuetype", "Value": 294, "Label": "VoIP System"}, {"Field": "subissuetype", "Value": 295, "Label": "Scanner"}, {"Field": "subissuetype", "Value": 296, "Label": "UPS"}, {"Field": "subissuetype", "Value": 297, "Label": "Per<PERSON>heral"}, {"Field": "subissuetype", "Value": 298, "Label": "Microsoft Access"}, {"Field": "subissuetype", "Value": 299, "Label": "Proofpoint"}, {"Field": "subissuetype", "Value": 300, "Label": "DarkWeb ID"}, {"Field": "subissuetype", "Value": 301, "Label": "Bitwarden"}, {"Field": "subissuetype", "Value": 302, "Label": "NinjaRMM"}, {"Field": "subissuetype", "Value": 303, "Label": "Nextiva"}, {"Field": "subissuetype", "Value": 304, "Label": "Onboarding"}, {"Field": "subissuetype", "Value": 305, "Label": "Offboarding"}, {"Field": "subissuetype", "Value": 306, "Label": "Autotask"}, {"Field": "subissuetype", "Value": 307, "Label": "Carbon Black"}, {"Field": "subissuetype", "Value": 310, "Label": "Microsoft Entra"}, {"Field": "subissuetype", "Value": 311, "Label": "Microsoft Power BI"}, {"Field": "subissuetype", "Value": 312, "Label": "Microsoft Power Apps"}, {"Field": "subissuetype", "Value": 313, "Label": "SIP Phone"}, {"Field": "subissuetype", "Value": 314, "Label": "Telephony"}, {"Field": "subissuetype", "Value": 315, "Label": "Impersonation"}, {"Field": "subissuetype", "Value": 316, "Label": "Sophos MDR"}, {"Field": "ticketcategory", "Value": 1, "Label": "Standard (non-editable)"}, {"Field": "ticketcategory", "Value": 2, "Label": "<PERSON><PERSON>"}, {"Field": "ticketcategory", "Value": 3, "Label": "Standard"}, {"Field": "ticketcategory", "Value": 4, "Label": "<PERSON><PERSON>"}, {"Field": "ticketcategory", "Value": 5, "Label": "RMA"}, {"Field": "ticketcategory", "Value": 6, "Label": "<PERSON>tto Networking Alert"}, {"Field": "ticketcategory", "Value": 101, "Label": "Projects"}, {"Field": "ticketcategory", "Value": 102, "Label": "Administration"}, {"Field": "ticketcategory", "Value": 103, "Label": "<PERSON><PERSON>"}, {"Field": "ticketcategory", "Value": 104, "Label": "Co-Managed"}, {"Field": "ticketcategory", "Value": 107, "Label": "NinjaRMM"}, {"Field": "ticketcategory", "Value": 108, "Label": "Recording & Measurements"}, {"Field": "ticketcategory", "Value": 109, "Label": "Opportunity"}, {"Field": "tickettype", "Value": 1, "Label": "Service Request"}, {"Field": "tickettype", "Value": 2, "Label": "Incident"}, {"Field": "tickettype", "Value": 3, "Label": "Problem"}, {"Field": "tickettype", "Value": 4, "Label": "Change Request"}, {"Field": "tickettype", "Value": 5, "Label": "<PERSON><PERSON>"}]